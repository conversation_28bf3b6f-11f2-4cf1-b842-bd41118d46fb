package com.example.aimusicplayer.ui.login

import android.content.Context
import android.util.Log
import android.widget.ImageView
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.bumptech.glide.request.RequestOptions
import com.example.aimusicplayer.R
import com.example.aimusicplayer.data.repository.UserRepository
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.json.JSONObject
import javax.inject.Inject

/**
 * 二维码处理器
 * 负责处理二维码登录相关的逻辑
 */
class QrCodeProcessor @Inject constructor(
    private val context: Context,
    private val userRepository: UserRepository
) {

    companion object {
        private const val TAG = "QrCodeProcessor"
        private const val QR_CHECK_INTERVAL = 3000L // 3秒检查一次二维码状态
    }

    // 二维码状态
    enum class QrStatus {
        LOADING,    // 加载中
        WAITING,    // 等待扫描
        SCANNED,    // 已扫描
        CONFIRMED,  // 已确认
        EXPIRED,    // 已过期
        ERROR       // 错误
    }

    // 二维码图片URL
    private val _qrImageUrl = MutableLiveData<String>()
    val qrImageUrl: LiveData<String> = _qrImageUrl

    // 二维码状态
    private val _qrStatus = MutableLiveData<QrStatus>()
    val qrStatus: LiveData<QrStatus> = _qrStatus

    // 二维码Key
    private var qrKey: String? = null

    // 检查二维码状态的Job
    private var checkQrStatusJob: Job? = null

    // 协程作用域
    private val coroutineScope = CoroutineScope(Dispatchers.Main)

    /**
     * 获取二维码Key - 参考ponymusic实现
     */
    suspend fun getQrKey(): String? {
        // 取消之前的检查任务
        stopCheckQrStatus()

        _qrStatus.value = QrStatus.LOADING
        qrKey = null
        _qrImageUrl.value = ""

        return try {
            // 调用API获取二维码Key
            val response = withContext(Dispatchers.IO) {
                userRepository.getQrKey()
            }

            // 解析响应
            val jsonObject = JSONObject(response)
            val code = jsonObject.optInt("code")

            if (code == 200) {
                val data = jsonObject.optJSONObject("data")
                if (data != null) {
                    qrKey = data.optString("unikey")
                    Log.d(TAG, "获取二维码Key成功: $qrKey")

                    if (!qrKey.isNullOrEmpty()) {
                        // 获取二维码图片URL
                        getQrImage(qrKey)
                        qrKey
                    } else {
                        Log.e(TAG, "获取到的二维码Key为空")
                        _qrStatus.value = QrStatus.ERROR
                        null
                    }
                } else {
                    Log.e(TAG, "获取二维码Key失败: 响应中没有data字段")
                    _qrStatus.value = QrStatus.ERROR
                    null
                }
            } else {
                val message = jsonObject.optString("message", "获取二维码Key失败")
                Log.e(TAG, "获取二维码Key失败: $message")
                _qrStatus.value = QrStatus.ERROR
                null
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取二维码Key异常", e)
            _qrStatus.value = QrStatus.ERROR
            null
        }
    }

    /**
     * 获取二维码图片URL
     */
    private suspend fun getQrImage(key: String?) {
        if (key.isNullOrEmpty()) {
            _qrStatus.value = QrStatus.ERROR
            return
        }

        try {
            // 调用API获取二维码图片URL
            val response = withContext(Dispatchers.IO) {
                userRepository.getQrImage(key)
            }

            // 解析响应
            val jsonObject = JSONObject(response)
            val code = jsonObject.optInt("code")

            if (code == 200) {
                val data = jsonObject.optJSONObject("data")
                if (data != null) {
                    val qrUrl = data.optString("qrimg")
                    Log.d(TAG, "获取二维码图片URL成功: $qrUrl")
                    _qrImageUrl.value = qrUrl
                    _qrStatus.value = QrStatus.WAITING

                    // 开始检查二维码状态
                    startCheckQrStatus()
                } else {
                    Log.e(TAG, "获取二维码图片URL失败: 响应中没有data字段")
                    _qrStatus.value = QrStatus.ERROR
                }
            } else {
                val message = jsonObject.optString("message", "获取二维码图片URL失败")
                Log.e(TAG, "获取二维码图片URL失败: $message")
                _qrStatus.value = QrStatus.ERROR
            }
        } catch (e: Exception) {
            Log.e(TAG, "获取二维码图片URL异常", e)
            _qrStatus.value = QrStatus.ERROR
        }
    }

    /**
     * 开始检查二维码状态 - 参考ponymusic实现
     */
    private fun startCheckQrStatus() {
        // 取消之前的检查任务
        checkQrStatusJob?.cancel()

        // 创建新的检查任务
        checkQrStatusJob = coroutineScope.launch {
            while (true) {
                try {
                    val shouldContinue = checkQrStatus()
                    if (!shouldContinue) {
                        break
                    }
                    delay(QR_CHECK_INTERVAL)
                } catch (e: Exception) {
                    Log.e(TAG, "检查二维码状态循环异常", e)
                    _qrStatus.value = QrStatus.ERROR
                    break
                }
            }
        }
    }

    /**
     * 检查二维码状态 - 参考ponymusic实现
     * @return Boolean 是否应该继续检查
     */
    private suspend fun checkQrStatus(): Boolean {
        if (qrKey.isNullOrEmpty()) {
            return false
        }

        try {
            // 调用API检查二维码状态
            val response = withContext(Dispatchers.IO) {
                userRepository.checkQrStatus(qrKey!!)
            }

            // 解析响应
            val jsonObject = JSONObject(response)
            val code = jsonObject.optInt("code")

            when (code) {
                800 -> {
                    // 二维码过期
                    Log.d(TAG, "二维码已过期")
                    _qrStatus.value = QrStatus.EXPIRED
                    return false
                }
                801 -> {
                    // 等待扫码
                    Log.d(TAG, "等待扫码")
                    _qrStatus.value = QrStatus.WAITING
                    return true
                }
                802 -> {
                    // 授权中
                    Log.d(TAG, "已扫码，等待确认")
                    _qrStatus.value = QrStatus.SCANNED
                    return true
                }
                803 -> {
                    // 授权登录成功
                    Log.d(TAG, "授权登录成功")
                    _qrStatus.value = QrStatus.CONFIRMED

                    // 获取cookie
                    val cookie = jsonObject.optString("cookie")
                    if (!cookie.isNullOrEmpty()) {
                        Log.d(TAG, "获取到cookie: ${cookie.length} 字符")
                        // 保存cookie
                        userRepository.saveCookie(cookie)
                    } else {
                        Log.e(TAG, "授权登录成功但没有cookie")
                        _qrStatus.value = QrStatus.ERROR
                    }
                    return false
                }
                else -> {
                    // 其他状态
                    val message = jsonObject.optString("message", "检查二维码状态失败")
                    Log.e(TAG, "检查二维码状态失败: $message")
                    _qrStatus.value = QrStatus.ERROR
                    return false
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "检查二维码状态异常", e)
            _qrStatus.value = QrStatus.ERROR
            return false
        }
    }

    /**
     * 加载二维码图片
     */
    fun loadQrImage(imageUrl: String, imageView: ImageView) {
        Glide.with(context)
            .load(imageUrl)
            .apply(RequestOptions()
                .placeholder(R.drawable.ic_qr_placeholder)
                .error(R.drawable.ic_qr_error)
                .diskCacheStrategy(DiskCacheStrategy.ALL))
            .into(imageView)
    }

    /**
     * 更新二维码状态
     */
    fun updateStatus(status: QrStatus) {
        _qrStatus.value = status
    }

    /**
     * 停止检查二维码状态
     */
    fun stopCheckQrStatus() {
        checkQrStatusJob?.cancel()
    }
}
