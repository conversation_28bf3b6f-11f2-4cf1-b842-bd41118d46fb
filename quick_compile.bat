@echo off
echo ========================================
echo 快速编译脚本 - AI音乐播放器
echo ========================================

:: 设置编码为UTF-8
chcp 65001 > nul

:: 检查gradlew.bat是否存在
if not exist "gradlew.bat" (
    echo 错误: gradlew.bat 文件不存在
    echo 请确保在项目根目录运行此脚本
    pause
    exit /b 1
)

echo 当前目录: %CD%
echo.

:: 显示菜单
echo 请选择编译选项:
echo 1. 快速编译检查 (compileDebugKotlin)
echo 2. 完整编译 (assembleDebug)
echo 3. 清理项目 (clean)
echo 4. 检查依赖 (dependencies)
echo 5. 查看任务列表 (tasks)
echo 6. 退出
echo.

set /p choice=请输入选项 (1-6): 

if "%choice%"=="1" goto quick_compile
if "%choice%"=="2" goto full_compile
if "%choice%"=="3" goto clean_project
if "%choice%"=="4" goto check_deps
if "%choice%"=="5" goto list_tasks
if "%choice%"=="6" goto exit
goto invalid_choice

:quick_compile
echo.
echo 执行快速编译检查...
echo ========================================
call gradlew.bat compileDebugKotlin --console=plain --no-daemon
goto end

:full_compile
echo.
echo 执行完整编译...
echo ========================================
call gradlew.bat assembleDebug --console=plain --no-daemon
goto end

:clean_project
echo.
echo 清理项目...
echo ========================================
call gradlew.bat clean --console=plain --no-daemon
goto end

:check_deps
echo.
echo 检查项目依赖...
echo ========================================
call gradlew.bat dependencies --console=plain --no-daemon
goto end

:list_tasks
echo.
echo 查看可用任务...
echo ========================================
call gradlew.bat tasks --console=plain --no-daemon
goto end

:invalid_choice
echo.
echo 无效选项，请重新运行脚本
pause
exit /b 1

:end
echo.
echo ========================================
echo 编译完成！
echo ========================================
pause

:exit
echo 退出脚本
