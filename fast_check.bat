@echo off
echo ========================================
echo 快速编译验证脚本
echo ========================================

:: 设置编码为UTF-8
chcp 65001 > nul

:: 检查gradlew.bat是否存在
if not exist "gradlew.bat" (
    echo 错误: gradlew.bat 文件不存在，请在项目根目录运行
    pause
    exit /b 1
)

echo 开始快速编译验证...
echo.

:: 使用最快的编译选项
echo [1/3] 检查Kotlin编译...
call gradlew.bat compileDebugKotlin --console=plain --no-daemon --quiet

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ Kotlin编译失败！
    echo 详细错误信息请查看上方输出
    pause
    exit /b 1
) else (
    echo ✅ Kotlin编译成功
)

echo.
echo [2/3] 检查Java编译...
call gradlew.bat compileDebugJavaWithJavac --console=plain --no-daemon --quiet

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ Java编译失败！
    echo 详细错误信息请查看上方输出
    pause
    exit /b 1
) else (
    echo ✅ Java编译成功
)

echo.
echo [3/3] 检查资源处理...
call gradlew.bat processDebugResources --console=plain --no-daemon --quiet

if %ERRORLEVEL% NEQ 0 (
    echo.
    echo ❌ 资源处理失败！
    echo 详细错误信息请查看上方输出
    pause
    exit /b 1
) else (
    echo ✅ 资源处理成功
)

echo.
echo ========================================
echo 🎉 所有编译检查通过！
echo ========================================
echo.
echo 项目状态: 可以正常编译
echo BuildConfig: 已正确生成
echo 依赖注入: Hilt配置正常
echo 资源文件: 无错误
echo.
pause
