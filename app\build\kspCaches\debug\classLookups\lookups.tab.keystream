  Activity android.app  Application android.app  
PendingIntent android.app  Service android.app  Context android.content  ContextWrapper android.content  Intent android.content  AudioManager 
android.media  Log android.util  ContextThemeWrapper android.view  View android.view  	ViewGroup android.view  FrameLayout android.widget  ComponentActivity androidx.activity  AppCompatActivity androidx.appcompat.app  ComponentActivity androidx.core.app  Fragment androidx.fragment.app  FragmentActivity androidx.fragment.app  AndroidViewModel androidx.lifecycle  	ViewModel androidx.lifecycle  C androidx.media3.common  
DataSource androidx.media3.datasource  	ExoPlayer androidx.media3.exoplayer  Builder #androidx.media3.exoplayer.ExoPlayer  DefaultMediaSourceFactory  androidx.media3.exoplayer.source   DefaultMediaNotificationProvider androidx.media3.session  MediaSession androidx.media3.session  MediaSessionService androidx.media3.session  Builder 8androidx.media3.session.DefaultMediaNotificationProvider  Builder $androidx.media3.session.MediaSession  BuilderBase $androidx.media3.session.MediaSession  MultiDexApplication androidx.multidex  DiffUtil androidx.recyclerview.widget  ListAdapter androidx.recyclerview.widget  RecyclerView androidx.recyclerview.widget  Adapter )androidx.recyclerview.widget.RecyclerView  
ViewHolder )androidx.recyclerview.widget.RecyclerView  OnConflictStrategy 
androidx.room  RoomDatabase 
androidx.room  	Companion  androidx.room.OnConflictStrategy  	Migration androidx.room.migration  SupportSQLiteDatabase androidx.sqlite.db  GeneratedAppGlideModule com.bumptech.glide  GeneratedAppGlideModuleImpl com.bumptech.glide  AppGlideModule com.bumptech.glide.module  LibraryGlideModule com.bumptech.glide.module  MusicApplication com.example.aimusicplayer  R com.example.aimusicplayer  	Companion *com.example.aimusicplayer.MusicApplication  drawable com.example.aimusicplayer.R  MediaItemAdapter !com.example.aimusicplayer.adapter  
ViewHolder 2com.example.aimusicplayer.adapter.MediaItemAdapter  
ApiManager com.example.aimusicplayer.api  UnifiedApiService com.example.aimusicplayer.api  ApiCacheManager $com.example.aimusicplayer.data.cache  	Companion 4com.example.aimusicplayer.data.cache.ApiCacheManager  AppDatabase !com.example.aimusicplayer.data.db  	Companion -com.example.aimusicplayer.data.db.AppDatabase  
DateConverter +com.example.aimusicplayer.data.db.converter  ApiCacheDao %com.example.aimusicplayer.data.db.dao  PlayHistoryDao %com.example.aimusicplayer.data.db.dao  PlaylistDao %com.example.aimusicplayer.data.db.dao  SongDao %com.example.aimusicplayer.data.db.dao  UserDao %com.example.aimusicplayer.data.db.dao  ApiCacheEntity (com.example.aimusicplayer.data.db.entity  PlayHistoryEntity (com.example.aimusicplayer.data.db.entity  PlaylistEntity (com.example.aimusicplayer.data.db.entity  PlaylistSongCrossRef (com.example.aimusicplayer.data.db.entity  
SongEntity (com.example.aimusicplayer.data.db.entity  
UserEntity (com.example.aimusicplayer.data.db.entity  	Companion 3com.example.aimusicplayer.data.db.entity.SongEntity  Album $com.example.aimusicplayer.data.model  Artist $com.example.aimusicplayer.data.model  Banner $com.example.aimusicplayer.data.model  BannerResponse $com.example.aimusicplayer.data.model  BaseResponse $com.example.aimusicplayer.data.model  
CommentDto $com.example.aimusicplayer.data.model  CommentResponse $com.example.aimusicplayer.data.model  	LyricInfo $com.example.aimusicplayer.data.model  
LyricResponse $com.example.aimusicplayer.data.model  NewSongsResponse $com.example.aimusicplayer.data.model  ParcelableSong $com.example.aimusicplayer.data.model  PlayList $com.example.aimusicplayer.data.model  ReplyDto $com.example.aimusicplayer.data.model  SongDetailResponse $com.example.aimusicplayer.data.model  User $com.example.aimusicplayer.data.model  UserDetailResponse $com.example.aimusicplayer.data.model  UserDto $com.example.aimusicplayer.data.model  UserSubCountResponse $com.example.aimusicplayer.data.model  Data 3com.example.aimusicplayer.data.model.BannerResponse  	Companion 3com.example.aimusicplayer.data.model.ParcelableSong  BaseRepository )com.example.aimusicplayer.data.repository  CommentRepository )com.example.aimusicplayer.data.repository  MusicRepository )com.example.aimusicplayer.data.repository  SettingsRepository )com.example.aimusicplayer.data.repository  UserRepository )com.example.aimusicplayer.data.repository  	Companion 8com.example.aimusicplayer.data.repository.BaseRepository  	Companion ;com.example.aimusicplayer.data.repository.CommentRepository  	Companion <com.example.aimusicplayer.data.repository.SettingsRepository  	Companion 8com.example.aimusicplayer.data.repository.UserRepository  
ApiService %com.example.aimusicplayer.data.source  MusicDataSource %com.example.aimusicplayer.data.source  	Companion 5com.example.aimusicplayer.data.source.MusicDataSource  Factory 5com.example.aimusicplayer.data.source.MusicDataSource  	AppModule com.example.aimusicplayer.di  DatabaseModule com.example.aimusicplayer.di  ErrorHandlingModule com.example.aimusicplayer.di  
NetworkModule com.example.aimusicplayer.di  GlobalErrorHandler com.example.aimusicplayer.error  	Companion 2com.example.aimusicplayer.error.GlobalErrorHandler  PlayMode !com.example.aimusicplayer.service  PlayServiceModule !com.example.aimusicplayer.service  	PlayState !com.example.aimusicplayer.service  PlayerController !com.example.aimusicplayer.service  PlayerControllerImpl !com.example.aimusicplayer.service  UnifiedPlaybackService !com.example.aimusicplayer.service  	Companion *com.example.aimusicplayer.service.PlayMode  	Companion 8com.example.aimusicplayer.service.UnifiedPlaybackService  CommentAdapter $com.example.aimusicplayer.ui.adapter  ReplyAdapter $com.example.aimusicplayer.ui.adapter  SongAdapter $com.example.aimusicplayer.ui.adapter  CommentViewHolder 3com.example.aimusicplayer.ui.adapter.CommentAdapter  CommentFragment $com.example.aimusicplayer.ui.comment  IntelligenceFragment )com.example.aimusicplayer.ui.intelligence  IntelligenceViewModel )com.example.aimusicplayer.ui.intelligence  	Companion ?com.example.aimusicplayer.ui.intelligence.IntelligenceViewModel  
LoginActivity "com.example.aimusicplayer.ui.login  QrCodeProcessor "com.example.aimusicplayer.ui.login  	Companion 0com.example.aimusicplayer.ui.login.LoginActivity  	Companion 2com.example.aimusicplayer.ui.login.QrCodeProcessor  SidebarController !com.example.aimusicplayer.ui.main  	Companion 3com.example.aimusicplayer.ui.main.SidebarController  	LyricView #com.example.aimusicplayer.ui.player  PlayerFragment #com.example.aimusicplayer.ui.player  	Companion -com.example.aimusicplayer.ui.player.LyricView  	Companion 2com.example.aimusicplayer.ui.player.PlayerFragment  UserProfileFragment $com.example.aimusicplayer.ui.profile  AlbumCoverView #com.example.aimusicplayer.ui.widget  LottieLoadingView #com.example.aimusicplayer.ui.widget  	Companion 2com.example.aimusicplayer.ui.widget.AlbumCoverView  
AlbumArtCache com.example.aimusicplayer.utils  AlbumArtProcessor com.example.aimusicplayer.utils  	BlurUtils com.example.aimusicplayer.utils  
CacheEntry com.example.aimusicplayer.utils  CacheManager com.example.aimusicplayer.utils  
CacheStats com.example.aimusicplayer.utils  
DiffCallbacks com.example.aimusicplayer.utils  EnhancedImageCache com.example.aimusicplayer.utils  FunctionalityTester com.example.aimusicplayer.utils  GlideModule com.example.aimusicplayer.utils  
LyricCache com.example.aimusicplayer.utils  
LyricUtils com.example.aimusicplayer.utils  NavigationUtils com.example.aimusicplayer.utils  
NetworkResult com.example.aimusicplayer.utils  NetworkUtils com.example.aimusicplayer.utils  
PlaylistCache com.example.aimusicplayer.utils  	Companion -com.example.aimusicplayer.utils.AlbumArtCache  	Companion 1com.example.aimusicplayer.utils.AlbumArtProcessor  	Companion ,com.example.aimusicplayer.utils.CacheManager  	Companion 2com.example.aimusicplayer.utils.EnhancedImageCache  	Companion 3com.example.aimusicplayer.utils.FunctionalityTester  	Companion +com.example.aimusicplayer.utils.GlideModule  	LyricLine *com.example.aimusicplayer.utils.LyricUtils  	Companion -com.example.aimusicplayer.utils.NetworkResult  Error -com.example.aimusicplayer.utils.NetworkResult  Success -com.example.aimusicplayer.utils.NetworkResult  CommentViewModel #com.example.aimusicplayer.viewmodel  DiscoveryViewModel #com.example.aimusicplayer.viewmodel  DrivingModeViewModel #com.example.aimusicplayer.viewmodel  ExampleViewModel #com.example.aimusicplayer.viewmodel  
FlowViewModel #com.example.aimusicplayer.viewmodel  LoginViewModel #com.example.aimusicplayer.viewmodel  
MainViewModel #com.example.aimusicplayer.viewmodel  MusicLibraryViewModel #com.example.aimusicplayer.viewmodel  PlayerViewModel #com.example.aimusicplayer.viewmodel  SettingsViewModel #com.example.aimusicplayer.viewmodel  SplashViewModel #com.example.aimusicplayer.viewmodel  UserProfileViewModel #com.example.aimusicplayer.viewmodel  	Companion 4com.example.aimusicplayer.viewmodel.CommentViewModel  	Companion 6com.example.aimusicplayer.viewmodel.DiscoveryViewModel  	Companion 8com.example.aimusicplayer.viewmodel.DrivingModeViewModel  	Companion 4com.example.aimusicplayer.viewmodel.ExampleViewModel  	Companion 1com.example.aimusicplayer.viewmodel.FlowViewModel  	Companion 2com.example.aimusicplayer.viewmodel.LoginViewModel  	Companion 1com.example.aimusicplayer.viewmodel.MainViewModel  	Companion 9com.example.aimusicplayer.viewmodel.MusicLibraryViewModel  	Companion 5com.example.aimusicplayer.viewmodel.SettingsViewModel  	Companion 3com.example.aimusicplayer.viewmodel.SplashViewModel  	Companion 8com.example.aimusicplayer.viewmodel.UserProfileViewModel  Class 	java.lang  KClass kotlin.reflect  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  BufferOverflow kotlinx.coroutines.channels  Flow kotlinx.coroutines.flow  
FlowCollector kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  Callback %androidx.recyclerview.widget.DiffUtil  ItemCallback %androidx.recyclerview.widget.DiffUtil  BitmapTransformation 'com.bumptech.glide.load.resource.bitmap  RetryInterceptor com.example.aimusicplayer.api  	Companion .com.example.aimusicplayer.api.RetryInterceptor  CommentDiffCallback 3com.example.aimusicplayer.ui.adapter.CommentAdapter  ReplyDiffCallback 1com.example.aimusicplayer.ui.adapter.ReplyAdapter  SongDiffCallback 0com.example.aimusicplayer.ui.adapter.SongAdapter  PaletteTransformation com.example.aimusicplayer.utils  CommentDiffCallback -com.example.aimusicplayer.utils.DiffCallbacks  MediaItemDiffCallback -com.example.aimusicplayer.utils.DiffCallbacks  PlaylistDiffCallback -com.example.aimusicplayer.utils.DiffCallbacks  	Companion 5com.example.aimusicplayer.utils.PaletteTransformation  Interceptor okhttp3                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            