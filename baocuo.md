 

> Task :app:compileDebugKotlin FAILED
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/MusicApplication.kt:9:34 Unresolved reference: BuildConfig
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/MusicApplication.kt:74:17 Unresolved reference: BuildConfig
e: file:///C:/Users/<USER>/Desktop/test/Android-Voice-Controlled-Music-Player-main/Android-Voice-Controlled-Music-Player-main/AIMusicPlayer1/app/src/main/java/com/example/aimusicplayer/MusicApplication.kt:108:13 Unresolved reference: BuildConfig

FAILURE: Build failed with an exception.

* What went wrong:
Execution failed for task ':app:compileDebugKotlin'.
> A failure occurred while executing org.jetbrains.kotlin.compilerRunner.GradleCompilerRunnerWithWorkers$GradleKotlinCompilerWorkAction
   > Compilation error. See log for more details

* Try:
> Run with --info or --debug option to get more log output.
> Run with --scan to get full insights.
> Get more help at https://help.gradle.org.

* Exception is:
org.gradle.api.tasks.TaskExecutionException: Execution failed for task ':app:compileDebugKotlin'.
	at org.gradle.api.internal.tasks.execution.ExecuteActionsTaskExecuter.lambda$executeIfValid$1(ExecuteActionsTaskExecuter.java:130)
	at org.gradle.internal.Try$Failure.ifSuccessfulOrElse(Try.java:293)
	at org.gradle.api.internal.tasks.execution.ExecuteActionsTaskExecuter.executeIfValid(ExecuteActionsTaskExecuter.java:128)
	at org.gradle.api.internal.tasks.execution.ExecuteActionsTaskExecuter.execute(ExecuteActionsTaskExecuter.java:116)
	at org.gradle.api.internal.tasks.execution.FinalizePropertiesTaskExecuter.execute(FinalizePropertiesTaskExecuter.java:46)
	at org.gradle.api.internal.tasks.execution.ResolveTaskExecutionModeExecuter.execute(ResolveTaskExecutionModeExecuter.java:51)
	at org.gradle.api.internal.tasks.execution.SkipTaskWithNoActionsExecuter.execute(SkipTaskWithNoActionsExecuter.java:57)
	at org.gradle.api.internal.tasks.execution.SkipOnlyIfTaskExecuter.execute(SkipOnlyIfTaskExecuter.java:74)
	at org.gradle.api.internal.tasks.execution.CatchExceptionTaskExecuter.execute(CatchExceptionTaskExecuter.java:36)
	at org.gradle.api.internal.tasks.execution.EventFiringTaskExecuter$1.executeTask(EventFiringTaskExecuter.java:77)
	at org.gradle.api.internal.tasks.execution.EventFiringTaskExecuter$1.call(EventFiringTaskExecuter.java:55)
	at org.gradle.api.internal.tasks.execution.EventFiringTaskExecuter$1.call(EventFiringTaskExecuter.java:52)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:209)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:204)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:166)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.call(DefaultBuildOperationRunner.java:53)
	at org.gradle.api.internal.tasks.execution.EventFiringTaskExecuter.execute(EventFiringTaskExecuter.java:52)
	at org.gradle.execution.plan.LocalTaskNodeExecutor.execute(LocalTaskNodeExecutor.java:42)
	at org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$InvokeNodeExecutorsAction.execute(DefaultTaskExecutionGraph.java:331)
	at org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$InvokeNodeExecutorsAction.execute(DefaultTaskExecutionGraph.java:318)
	at org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$BuildOperationAwareExecutionAction.lambda$execute$0(DefaultTaskExecutionGraph.java:314)
	at org.gradle.internal.operations.CurrentBuildOperationRef.with(CurrentBuildOperationRef.java:85)
	at org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$BuildOperationAwareExecutionAction.execute(DefaultTaskExecutionGraph.java:314)
	at org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$BuildOperationAwareExecutionAction.execute(DefaultTaskExecutionGraph.java:303)
	at org.gradle.execution.plan.DefaultPlanExecutor$ExecutorWorker.execute(DefaultPlanExecutor.java:459)
	at org.gradle.execution.plan.DefaultPlanExecutor$ExecutorWorker.run(DefaultPlanExecutor.java:376)
	at org.gradle.internal.concurrent.ExecutorPolicy$CatchAndRecordFailures.onExecute(ExecutorPolicy.java:64)
	at org.gradle.internal.concurrent.AbstractManagedExecutor$1.run(AbstractManagedExecutor.java:48)
Caused by: org.gradle.workers.internal.DefaultWorkerExecutor$WorkExecutionException: A failure occurred while executing org.jetbrains.kotlin.compilerRunner.GradleCompilerRunnerWithWorkers$GradleKotlinCompilerWorkAction
	at org.gradle.workers.internal.DefaultWorkerExecutor$WorkItemExecution.waitForCompletion(DefaultWorkerExecutor.java:287)
	at org.gradle.internal.work.DefaultAsyncWorkTracker.lambda$waitForItemsAndGatherFailures$2(DefaultAsyncWorkTracker.java:130)
	at org.gradle.internal.Factories$1.create(Factories.java:31)
	at org.gradle.internal.work.DefaultWorkerLeaseService.withoutLocks(DefaultWorkerLeaseService.java:335)
	at org.gradle.internal.work.DefaultWorkerLeaseService.withoutLocks(DefaultWorkerLeaseService.java:318)
	at org.gradle.internal.work.DefaultWorkerLeaseService.withoutLock(DefaultWorkerLeaseService.java:323)
	at org.gradle.internal.work.DefaultAsyncWorkTracker.waitForItemsAndGatherFailures(DefaultAsyncWorkTracker.java:126)
	at org.gradle.internal.work.DefaultAsyncWorkTracker.waitForItemsAndGatherFailures(DefaultAsyncWorkTracker.java:92)
	at org.gradle.internal.work.DefaultAsyncWorkTracker.waitForAll(DefaultAsyncWorkTracker.java:78)
	at org.gradle.internal.work.DefaultAsyncWorkTracker.waitForCompletion(DefaultAsyncWorkTracker.java:66)
	at org.gradle.api.internal.tasks.execution.TaskExecution$3.run(TaskExecution.java:252)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(DefaultBuildOperationRunner.java:29)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$1.execute(DefaultBuildOperationRunner.java:26)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:166)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.run(DefaultBuildOperationRunner.java:47)
	at org.gradle.api.internal.tasks.execution.TaskExecution.executeAction(TaskExecution.java:229)
	at org.gradle.api.internal.tasks.execution.TaskExecution.executeActions(TaskExecution.java:212)
	at org.gradle.api.internal.tasks.execution.TaskExecution.executeWithPreviousOutputFiles(TaskExecution.java:195)
	at org.gradle.api.internal.tasks.execution.TaskExecution.execute(TaskExecution.java:162)
	at org.gradle.internal.execution.steps.ExecuteStep.executeInternal(ExecuteStep.java:105)
	at org.gradle.internal.execution.steps.ExecuteStep.access$000(ExecuteStep.java:44)
	at org.gradle.internal.execution.steps.ExecuteStep$1.call(ExecuteStep.java:59)
	at org.gradle.internal.execution.steps.ExecuteStep$1.call(ExecuteStep.java:56)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:209)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:204)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:166)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.call(DefaultBuildOperationRunner.java:53)
	at org.gradle.internal.execution.steps.ExecuteStep.execute(ExecuteStep.java:56)
	at org.gradle.internal.execution.steps.ExecuteStep.execute(ExecuteStep.java:44)
	at org.gradle.internal.execution.steps.CancelExecutionStep.execute(CancelExecutionStep.java:42)
	at org.gradle.internal.execution.steps.TimeoutStep.executeWithoutTimeout(TimeoutStep.java:75)
	at org.gradle.internal.execution.steps.TimeoutStep.execute(TimeoutStep.java:55)
	at org.gradle.internal.execution.steps.PreCreateOutputParentsStep.execute(PreCreateOutputParentsStep.java:50)
	at org.gradle.internal.execution.steps.PreCreateOutputParentsStep.execute(PreCreateOutputParentsStep.java:28)
	at org.gradle.internal.execution.steps.RemovePreviousOutputsStep.execute(RemovePreviousOutputsStep.java:67)
	at org.gradle.internal.execution.steps.RemovePreviousOutputsStep.execute(RemovePreviousOutputsStep.java:37)
	at org.gradle.internal.execution.steps.BroadcastChangingOutputsStep.execute(BroadcastChangingOutputsStep.java:61)
	at org.gradle.internal.execution.steps.BroadcastChangingOutputsStep.execute(BroadcastChangingOutputsStep.java:26)
	at org.gradle.internal.execution.steps.CaptureOutputsAfterExecutionStep.execute(CaptureOutputsAfterExecutionStep.java:69)
	at org.gradle.internal.execution.steps.CaptureOutputsAfterExecutionStep.execute(CaptureOutputsAfterExecutionStep.java:46)
	at org.gradle.internal.execution.steps.ResolveInputChangesStep.execute(ResolveInputChangesStep.java:40)
	at org.gradle.internal.execution.steps.ResolveInputChangesStep.execute(ResolveInputChangesStep.java:29)
	at org.gradle.internal.execution.steps.BuildCacheStep.executeWithoutCache(BuildCacheStep.java:189)
	at org.gradle.internal.execution.steps.BuildCacheStep.executeAndStoreInCache(BuildCacheStep.java:145)
	at org.gradle.internal.execution.steps.BuildCacheStep.lambda$executeWithCache$4(BuildCacheStep.java:101)
	at org.gradle.internal.execution.steps.BuildCacheStep.lambda$executeWithCache$5(BuildCacheStep.java:101)
	at org.gradle.internal.Try$Success.map(Try.java:175)
	at org.gradle.internal.execution.steps.BuildCacheStep.executeWithCache(BuildCacheStep.java:85)
	at org.gradle.internal.execution.steps.BuildCacheStep.lambda$execute$0(BuildCacheStep.java:74)
	at org.gradle.internal.Either$Left.fold(Either.java:115)
	at org.gradle.internal.execution.caching.CachingState.fold(CachingState.java:62)
	at org.gradle.internal.execution.steps.BuildCacheStep.execute(BuildCacheStep.java:73)
	at org.gradle.internal.execution.steps.BuildCacheStep.execute(BuildCacheStep.java:48)
	at org.gradle.internal.execution.steps.StoreExecutionStateStep.execute(StoreExecutionStateStep.java:46)
	at org.gradle.internal.execution.steps.StoreExecutionStateStep.execute(StoreExecutionStateStep.java:35)
	at org.gradle.internal.execution.steps.SkipUpToDateStep.executeBecause(SkipUpToDateStep.java:75)
	at org.gradle.internal.execution.steps.SkipUpToDateStep.lambda$execute$2(SkipUpToDateStep.java:53)
	at org.gradle.internal.execution.steps.SkipUpToDateStep.execute(SkipUpToDateStep.java:53)
	at org.gradle.internal.execution.steps.SkipUpToDateStep.execute(SkipUpToDateStep.java:35)
	at org.gradle.internal.execution.steps.legacy.MarkSnapshottingInputsFinishedStep.execute(MarkSnapshottingInputsFinishedStep.java:37)
	at org.gradle.internal.execution.steps.legacy.MarkSnapshottingInputsFinishedStep.execute(MarkSnapshottingInputsFinishedStep.java:27)
	at org.gradle.internal.execution.steps.ResolveIncrementalCachingStateStep.executeDelegate(ResolveIncrementalCachingStateStep.java:49)
	at org.gradle.internal.execution.steps.ResolveIncrementalCachingStateStep.executeDelegate(ResolveIncrementalCachingStateStep.java:27)
	at org.gradle.internal.execution.steps.AbstractResolveCachingStateStep.execute(AbstractResolveCachingStateStep.java:71)
	at org.gradle.internal.execution.steps.AbstractResolveCachingStateStep.execute(AbstractResolveCachingStateStep.java:39)
	at org.gradle.internal.execution.steps.ResolveChangesStep.execute(ResolveChangesStep.java:65)
	at org.gradle.internal.execution.steps.ResolveChangesStep.execute(ResolveChangesStep.java:36)
	at org.gradle.internal.execution.steps.ValidateStep.execute(ValidateStep.java:107)
	at org.gradle.internal.execution.steps.ValidateStep.execute(ValidateStep.java:56)
	at org.gradle.internal.execution.steps.AbstractCaptureStateBeforeExecutionStep.execute(AbstractCaptureStateBeforeExecutionStep.java:64)
	at org.gradle.internal.execution.steps.AbstractCaptureStateBeforeExecutionStep.execute(AbstractCaptureStateBeforeExecutionStep.java:43)
	at org.gradle.internal.execution.steps.AbstractSkipEmptyWorkStep.executeWithNonEmptySources(AbstractSkipEmptyWorkStep.java:125)
	at org.gradle.internal.execution.steps.AbstractSkipEmptyWorkStep.execute(AbstractSkipEmptyWorkStep.java:61)
	at org.gradle.internal.execution.steps.AbstractSkipEmptyWorkStep.execute(AbstractSkipEmptyWorkStep.java:36)
	at org.gradle.internal.execution.steps.legacy.MarkSnapshottingInputsStartedStep.execute(MarkSnapshottingInputsStartedStep.java:38)
	at org.gradle.internal.execution.steps.LoadPreviousExecutionStateStep.execute(LoadPreviousExecutionStateStep.java:36)
	at org.gradle.internal.execution.steps.LoadPreviousExecutionStateStep.execute(LoadPreviousExecutionStateStep.java:23)
	at org.gradle.internal.execution.steps.HandleStaleOutputsStep.execute(HandleStaleOutputsStep.java:75)
	at org.gradle.internal.execution.steps.HandleStaleOutputsStep.execute(HandleStaleOutputsStep.java:41)
	at org.gradle.internal.execution.steps.AssignMutableWorkspaceStep.lambda$execute$0(AssignMutableWorkspaceStep.java:35)
	at org.gradle.api.internal.tasks.execution.TaskExecution$4.withWorkspace(TaskExecution.java:289)
	at org.gradle.internal.execution.steps.AssignMutableWorkspaceStep.execute(AssignMutableWorkspaceStep.java:31)
	at org.gradle.internal.execution.steps.AssignMutableWorkspaceStep.execute(AssignMutableWorkspaceStep.java:22)
	at org.gradle.internal.execution.steps.ChoosePipelineStep.execute(ChoosePipelineStep.java:40)
	at org.gradle.internal.execution.steps.ChoosePipelineStep.execute(ChoosePipelineStep.java:23)
	at org.gradle.internal.execution.steps.ExecuteWorkBuildOperationFiringStep.lambda$execute$2(ExecuteWorkBuildOperationFiringStep.java:67)
	at org.gradle.internal.execution.steps.ExecuteWorkBuildOperationFiringStep.execute(ExecuteWorkBuildOperationFiringStep.java:67)
	at org.gradle.internal.execution.steps.ExecuteWorkBuildOperationFiringStep.execute(ExecuteWorkBuildOperationFiringStep.java:39)
	at org.gradle.internal.execution.steps.IdentityCacheStep.execute(IdentityCacheStep.java:46)
	at org.gradle.internal.execution.steps.IdentityCacheStep.execute(IdentityCacheStep.java:34)
	at org.gradle.internal.execution.steps.IdentifyStep.execute(IdentifyStep.java:48)
	at org.gradle.internal.execution.steps.IdentifyStep.execute(IdentifyStep.java:35)
	at org.gradle.internal.execution.impl.DefaultExecutionEngine$1.execute(DefaultExecutionEngine.java:61)
	at org.gradle.api.internal.tasks.execution.ExecuteActionsTaskExecuter.executeIfValid(ExecuteActionsTaskExecuter.java:127)
	at org.gradle.api.internal.tasks.execution.ExecuteActionsTaskExecuter.execute(ExecuteActionsTaskExecuter.java:116)
	at org.gradle.api.internal.tasks.execution.FinalizePropertiesTaskExecuter.execute(FinalizePropertiesTaskExecuter.java:46)
	at org.gradle.api.internal.tasks.execution.ResolveTaskExecutionModeExecuter.execute(ResolveTaskExecutionModeExecuter.java:51)
	at org.gradle.api.internal.tasks.execution.SkipTaskWithNoActionsExecuter.execute(SkipTaskWithNoActionsExecuter.java:57)
	at org.gradle.api.internal.tasks.execution.SkipOnlyIfTaskExecuter.execute(SkipOnlyIfTaskExecuter.java:74)
	at org.gradle.api.internal.tasks.execution.CatchExceptionTaskExecuter.execute(CatchExceptionTaskExecuter.java:36)
	at org.gradle.api.internal.tasks.execution.EventFiringTaskExecuter$1.executeTask(EventFiringTaskExecuter.java:77)
	at org.gradle.api.internal.tasks.execution.EventFiringTaskExecuter$1.call(EventFiringTaskExecuter.java:55)
	at org.gradle.api.internal.tasks.execution.EventFiringTaskExecuter$1.call(EventFiringTaskExecuter.java:52)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:209)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:204)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:166)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.call(DefaultBuildOperationRunner.java:53)
	at org.gradle.api.internal.tasks.execution.EventFiringTaskExecuter.execute(EventFiringTaskExecuter.java:52)
	at org.gradle.execution.plan.LocalTaskNodeExecutor.execute(LocalTaskNodeExecutor.java:42)
	at org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$InvokeNodeExecutorsAction.execute(DefaultTaskExecutionGraph.java:331)
	at org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$InvokeNodeExecutorsAction.execute(DefaultTaskExecutionGraph.java:318)
	at org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$BuildOperationAwareExecutionAction.lambda$execute$0(DefaultTaskExecutionGraph.java:314)
	at org.gradle.internal.operations.CurrentBuildOperationRef.with(CurrentBuildOperationRef.java:85)
	at org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$BuildOperationAwareExecutionAction.execute(DefaultTaskExecutionGraph.java:314)
	at org.gradle.execution.taskgraph.DefaultTaskExecutionGraph$BuildOperationAwareExecutionAction.execute(DefaultTaskExecutionGraph.java:303)
	at org.gradle.execution.plan.DefaultPlanExecutor$ExecutorWorker.execute(DefaultPlanExecutor.java:459)
	at org.gradle.execution.plan.DefaultPlanExecutor$ExecutorWorker.run(DefaultPlanExecutor.java:376)
	at org.gradle.internal.concurrent.ExecutorPolicy$CatchAndRecordFailures.onExecute(ExecutorPolicy.java:64)
	at org.gradle.internal.concurrent.AbstractManagedExecutor$1.run(AbstractManagedExecutor.java:48)
Caused by: org.jetbrains.kotlin.gradle.tasks.CompilationErrorException: Compilation error. See log for more details
	at org.jetbrains.kotlin.gradle.tasks.TasksUtilsKt.throwExceptionIfCompilationFailed(tasksUtils.kt:20)
	at org.jetbrains.kotlin.compilerRunner.GradleKotlinCompilerWork.run(GradleKotlinCompilerWork.kt:141)
	at org.jetbrains.kotlin.compilerRunner.GradleCompilerRunnerWithWorkers$GradleKotlinCompilerWorkAction.execute(GradleCompilerRunnerWithWorkers.kt:73)
	at org.gradle.workers.internal.DefaultWorkerServer.execute(DefaultWorkerServer.java:63)
	at org.gradle.workers.internal.NoIsolationWorkerFactory$1$1.create(NoIsolationWorkerFactory.java:66)
	at org.gradle.workers.internal.NoIsolationWorkerFactory$1$1.create(NoIsolationWorkerFactory.java:62)
	at org.gradle.internal.classloader.ClassLoaderUtils.executeInClassloader(ClassLoaderUtils.java:100)
	at org.gradle.workers.internal.NoIsolationWorkerFactory$1.lambda$execute$0(NoIsolationWorkerFactory.java:62)
	at org.gradle.workers.internal.AbstractWorker$1.call(AbstractWorker.java:44)
	at org.gradle.workers.internal.AbstractWorker$1.call(AbstractWorker.java:41)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:209)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$CallableBuildOperationWorker.execute(DefaultBuildOperationRunner.java:204)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:66)
	at org.gradle.internal.operations.DefaultBuildOperationRunner$2.execute(DefaultBuildOperationRunner.java:59)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:166)
	at org.gradle.internal.operations.DefaultBuildOperationRunner.execute(DefaultBuildOperationRunner.java:59)                                                                                             	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
2025-05-24 18:10:31.709  6298-6298  LoginActivity           com.example.aimusicplayer            E  错误信息: 未能获取账号信息
2025-05-24 18:10:31.783  6298-6320  OpenGLRenderer          com.example.aimusicplayer            E  Unable to match the desired swap behavior.
2025-05-24 18:10:39.684  6298-6298  FlowViewModel           com.example.aimusicplayer            E  Error in ViewModel (Ask Gemini)
                                                                                                    java.lang.Exception: 未能获取账号信息
                                                                                                    	at com.example.aimusicplayer.viewmodel.LoginViewModel.getUserInfo(LoginViewModel.kt:319)
                                                                                                    	at com.example.aimusicplayer.viewmodel.LoginViewModel.access$getUserInfo(LoginViewModel.kt:30)
                                                                                                    	at com.example.aimusicplayer.viewmodel.LoginViewModel$getUserInfo$1.invokeSuspend(Unknown Source:14)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:942)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:7924)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
2025-05-24 18:10:39.684  6298-6298  GlobalErrorHandler      com.example.aimusicplayer            E  处理错误 (Ask Gemini)
                                                                                                    java.lang.Exception: 未能获取账号信息
                                                                                                    	at com.example.aimusicplayer.viewmodel.LoginViewModel.getUserInfo(LoginViewModel.kt:319)
                                                                                                    	at com.example.aimusicplayer.viewmodel.LoginViewModel.access$getUserInfo(LoginViewModel.kt:30)
                                                                                                    	at com.example.aimusicplayer.viewmodel.LoginViewModel$getUserInfo$1.invokeSuspend(Unknown Source:14)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:942)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:7924)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
2025-05-24 18:10:39.692  6298-6298  LoginActivity           com.example.aimusicplayer            E  错误信息: 未能获取账号信息
2025-05-24 18:10:39.764  6298-6320  OpenGLRenderer          com.example.aimusicplayer            E  Unable to match the desired swap behavior.
2025-05-24 18:10:39.935   616-795   WifiScoringParams       system_server                        E  Invalid frequency(-1), using 5G as default rssi array
2025-05-24 18:10:55.200  2980-6746  FirebaseInstanceId      com.android.vending                  E  Failed to get FIS auth token (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.google.firebase.installations.FirebaseInstallationsException: Firebase Installations Service is unavailable. Please try again later.
                                                                                                    	at vtw.iJ(PG:32)
                                                                                                    	at vtw.G(PG:31)
                                                                                                    	at afgv.i(PG:183)
                                                                                                    	at acdh.a(PG:191)
                                                                                                    	at ujn.run(PG:251)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at vbl.run(PG:7)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    Caused by: com.google.firebase.installations.FirebaseInstallationsException: Firebase Installations Service is unavailable. Please try again later.
                                                                                                    	at acei.b(PG:356)
                                                                                                    	at acdu.run(PG:233)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
2025-05-24 18:10:59.735  2980-3224  PlayCommon              com.android.vending                  E  [218] ypn.j(1620): Failed to connect to server for server timestamp: java.net.SocketTimeoutException: failed to connect to play.googleapis.com/2001:4860:4802:38::223 (port 443) from /fec0::5054:ff:fe12:3456 (port 45914) after 40000ms
2025-05-24 18:11:00.096  3304-6759  FirebaseInstanceId      com.android.vending                  E  Failed to get FIS auth token (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.google.firebase.installations.FirebaseInstallationsException: Firebase Installations Service is unavailable. Please try again later.
                                                                                                    	at vtw.iJ(PG:32)
                                                                                                    	at vtw.G(PG:31)
                                                                                                    	at afgv.i(PG:183)
                                                                                                    	at acdh.a(PG:191)
                                                                                                    	at ujn.run(PG:251)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at vbl.run(PG:7)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    Caused by: com.google.firebase.installations.FirebaseInstallationsException: Firebase Installations Service is unavailable. Please try again later.
                                                                                                    	at acei.b(PG:356)
                                                                                                    	at acdu.run(PG:233)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
2025-05-24 18:11:05.131  3304-3540  PlayCommon              com.android.vending                  E  [239] ypn.j(1620): Failed to connect to server for server timestamp: java.net.SocketTimeoutException: failed to connect to play.googleapis.com/2001:4860:4802:38::223 (port 443) from /fec0::5054:ff:fe12:3456 (port 52302) after 40000ms
2025-05-24 18:11:17.977  1539-6166  WakeLock                com.google.android.gms.persistent    E  GCM_HB_ALARM release without a matched acquire!
2025-05-24 18:11:50.407   173-173   keystore2               keystore2                            E  keystore2::remote_provisioning: In get_remote_provisioning_key_and_certs: Error occurred: In get_rem_prov_attest_key: Failed to get a key
                                                                                                    
                                                                                                    Caused by:
                                                                                                        0: In get_rem_prov_attest_key_helper: Failed to assign a key
                                                                                                        1: In assign_attestation_key: 
                                                                                                        2: In with_transaction.
                                                                                                        3: Out of keys.
                                                                                                        4: Error::Rc(ResponseCode(22))
2025-05-24 18:11:50.453  7034-7034  moteprovisioner         com.android.remoteprovisioner        E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 18:11:55.933   173-173   keystore2               keystore2                            E  keystore2::remote_provisioning: In get_remote_provisioning_key_and_certs: Error occurred: In get_rem_prov_attest_key: Failed to get a key
                                                                                                    
                                                                                                    Caused by:
                                                                                                        0: In get_rem_prov_attest_key_helper: Failed to assign a key
                                                                                                        1: In assign_attestation_key: 
                                                                                                        2: In with_transaction.
                                                                                                        3: Out of keys.
                                                                                                        4: Error::Rc(ResponseCode(22))
2025-05-24 18:12:02.033  6298-6320  OpenGLRenderer          com.example.aimusicplayer            E  Unable to match the desired swap behavior.
2025-05-24 18:12:19.395   344-344   android.ha...rvice-mock <EMAIL>  E  Failed to getEnergyData
                                                                                             	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
2025-05-24 18:10:31.709  6298-6298  LoginActivity           com.example.aimusicplayer            E  错误信息: 未能获取账号信息
2025-05-24 18:10:31.783  6298-6320  OpenGLRenderer          com.example.aimusicplayer            E  Unable to match the desired swap behavior.
2025-05-24 18:10:39.684  6298-6298  FlowViewModel           com.example.aimusicplayer            E  Error in ViewModel (Ask Gemini)
                                                                                                    java.lang.Exception: 未能获取账号信息
                                                                                                    	at com.example.aimusicplayer.viewmodel.LoginViewModel.getUserInfo(LoginViewModel.kt:319)
                                                                                                    	at com.example.aimusicplayer.viewmodel.LoginViewModel.access$getUserInfo(LoginViewModel.kt:30)
                                                                                                    	at com.example.aimusicplayer.viewmodel.LoginViewModel$getUserInfo$1.invokeSuspend(Unknown Source:14)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:942)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:7924)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
2025-05-24 18:10:39.684  6298-6298  GlobalErrorHandler      com.example.aimusicplayer            E  处理错误 (Ask Gemini)
                                                                                                    java.lang.Exception: 未能获取账号信息
                                                                                                    	at com.example.aimusicplayer.viewmodel.LoginViewModel.getUserInfo(LoginViewModel.kt:319)
                                                                                                    	at com.example.aimusicplayer.viewmodel.LoginViewModel.access$getUserInfo(LoginViewModel.kt:30)
                                                                                                    	at com.example.aimusicplayer.viewmodel.LoginViewModel$getUserInfo$1.invokeSuspend(Unknown Source:14)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:942)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:7924)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
2025-05-24 18:10:39.692  6298-6298  LoginActivity           com.example.aimusicplayer            E  错误信息: 未能获取账号信息
2025-05-24 18:10:39.764  6298-6320  OpenGLRenderer          com.example.aimusicplayer            E  Unable to match the desired swap behavior.
2025-05-24 18:10:39.935   616-795   WifiScoringParams       system_server                        E  Invalid frequency(-1), using 5G as default rssi array
2025-05-24 18:10:55.200  2980-6746  FirebaseInstanceId      com.android.vending                  E  Failed to get FIS auth token (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.google.firebase.installations.FirebaseInstallationsException: Firebase Installations Service is unavailable. Please try again later.
                                                                                                    	at vtw.iJ(PG:32)
                                                                                                    	at vtw.G(PG:31)
                                                                                                    	at afgv.i(PG:183)
                                                                                                    	at acdh.a(PG:191)
                                                                                                    	at ujn.run(PG:251)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at vbl.run(PG:7)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    Caused by: com.google.firebase.installations.FirebaseInstallationsException: Firebase Installations Service is unavailable. Please try again later.
                                                                                                    	at acei.b(PG:356)
                                                                                                    	at acdu.run(PG:233)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
2025-05-24 18:10:59.735  2980-3224  PlayCommon              com.android.vending                  E  [218] ypn.j(1620): Failed to connect to server for server timestamp: java.net.SocketTimeoutException: failed to connect to play.googleapis.com/2001:4860:4802:38::223 (port 443) from /fec0::5054:ff:fe12:3456 (port 45914) after 40000ms
2025-05-24 18:11:00.096  3304-6759  FirebaseInstanceId      com.android.vending                  E  Failed to get FIS auth token (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.google.firebase.installations.FirebaseInstallationsException: Firebase Installations Service is unavailable. Please try again later.
                                                                                                    	at vtw.iJ(PG:32)
                                                                                                    	at vtw.G(PG:31)
                                                                                                    	at afgv.i(PG:183)
                                                                                                    	at acdh.a(PG:191)
                                                                                                    	at ujn.run(PG:251)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at vbl.run(PG:7)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    Caused by: com.google.firebase.installations.FirebaseInstallationsException: Firebase Installations Service is unavailable. Please try again later.
                                                                                                    	at acei.b(PG:356)
                                                                                                    	at acdu.run(PG:233)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
2025-05-24 18:11:05.131  3304-3540  PlayCommon              com.android.vending                  E  [239] ypn.j(1620): Failed to connect to server for server timestamp: java.net.SocketTimeoutException: failed to connect to play.googleapis.com/2001:4860:4802:38::223 (port 443) from /fec0::5054:ff:fe12:3456 (port 52302) after 40000ms
2025-05-24 18:11:17.977  1539-6166  WakeLock                com.google.android.gms.persistent    E  GCM_HB_ALARM release without a matched acquire!
2025-05-24 18:11:50.407   173-173   keystore2               keystore2                            E  keystore2::remote_provisioning: In get_remote_provisioning_key_and_certs: Error occurred: In get_rem_prov_attest_key: Failed to get a key
                                                                                                    
                                                                                                    Caused by:
                                                                                                        0: In get_rem_prov_attest_key_helper: Failed to assign a key
                                                                                                        1: In assign_attestation_key: 
                                                                                                        2: In with_transaction.
                                                                                                        3: Out of keys.
                                                                                                        4: Error::Rc(ResponseCode(22))
2025-05-24 18:11:50.453  7034-7034  moteprovisioner         com.android.remoteprovisioner        E  Not starting debugger since process cannot load the jdwp agent.
2025-05-24 18:11:55.933   173-173   keystore2               keystore2                            E  keystore2::remote_provisioning: In get_remote_provisioning_key_and_certs: Error occurred: In get_rem_prov_attest_key: Failed to get a key
                                                                                                    
                                                                                                    Caused by:
                                                                                                        0: In get_rem_prov_attest_key_helper: Failed to assign a key
                                                                                                        1: In assign_attestation_key: 
                                                                                                        2: In with_transaction.
                                                                                                        3: Out of keys.
                                                                                                        4: Error::Rc(ResponseCode(22))
2025-05-24 18:12:02.033  6298-6320  OpenGLRenderer          com.example.aimusicplayer            E  Unable to match the desired swap behavior.
2025-05-24 18:12:19.395   344-344   android.ha...rvice-mock <EMAIL>  E  Failed to getEnergyData

	at org.gradle.internal.operations.DefaultBuildOperationRunner.call(DefaultBuildOperationRunner.java:53)
	at org.gradle.workers.internal.AbstractWorker.executeWrappedInBuildOperation(AbstractWorker.java:41)
	at org.gradle.workers.internal.NoIsolationWorkerFactory$1.execute(NoIsolationWorkerFactory.java:59)
	at org.gradle.workers.internal.DefaultWorkerExecutor.lambda$submitWork$0(DefaultWorkerExecutor.java:174)
	at org.gradle.internal.work.DefaultConditionalExecutionQueue$ExecutionRunner.runExecution(DefaultConditionalExecutionQueue.java:194)
	at org.gradle.internal.work.DefaultConditionalExecutionQueue$ExecutionRunner.access$700(DefaultConditionalExecutionQueue.java:127)
	at org.gradle.internal.work.DefaultConditionalExecutionQueue$ExecutionRunner$1.run(DefaultConditionalExecutionQueue.java:169)
	at org.gradle.internal.Factories$1.create(Factories.java:31)
	at org.gradle.internal.work.DefaultWorkerLeaseService.withLocks(DefaultWorkerLeaseService.java:263)
	at org.gradle.internal.work.DefaultWorkerLeaseService.runAsWorkerThread(DefaultWorkerLeaseService.java:127)
	at org.gradle.internal.work.DefaultWorkerLeaseService.runAsWorkerThread(DefaultWorkerLeaseService.java:132)
	at org.gradle.internal.work.DefaultConditionalExecutionQueue$ExecutionRunner.runBatch(DefaultConditionalExecutionQueue.java:164)
	at org.gradle.internal.work.DefaultConditionalExecutionQueue$ExecutionRunner.run(DefaultConditionalExecutionQueue.java:133)
	... 2 more


BUILD FAILED in 47s
33 actionable tasks: 3 executed, 4 from cache, 26 up-to-date
                                                                                                    	at bnen.M(:com.google.android.gms@242632114@24.26.32 (230800-650348549):17)
                                                                                                    	at bnen.q(:com.google.android.gms@242632114@24.26.32 (230800-650348549):113) 
                                                                                                    	at bnen.B(:com.google.android.gms@242632114@24.26.32 (230800-650348549):10) 
                                                                                                    	at bnen.p(:com.google.android.gms@242632114@24.26.32 (230800-650348549):11) 
                                                                                                    	at com.google.android.gms.phenotype.sync.HeterodyneSyncTaskChimeraService.d(:com.google.android.gms@242632114@24.26.32 (230800-650348549):270) 
                                                                                                    	at com.google.android.gms.phenotype.sync.HeterodyneSyncTaskChimeraService.a(:com.google.android.gms@242632114@24.26.32 (230800-650348549):126) 
                                                                                                    	at avkn.call(:com.google.android.gms@242632114@24.26.32 (230800-650348549):32) 
                                                                                                    	at java.util.concurrent.FutureTask.run(FutureTask.java:264) 
                                                                                                    	at adtm.c(:com.google.android.gms@242632114@24.26.32 (230800-650348549):50) 
                                                                                                    	at adtm.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):76) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at adys.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):8) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    Caused by: avee: Failed to process request
                                                                                                    	at avhq.b(:com.google.android.gms@242632114@24.26.32 (230800-650348549):19)
                                                                                                    	at avie.onFailed(:com.google.android.gms@242632114@24.26.32 (230800-650348549):15)
                                                                                                    	at m.ms.onFailed(:com.google.android.gms.dynamite_cronetdynamite@242632114@24.26.32 (230800-0):3)
                                                                                                    	at m.lt.run(:com.google.android.gms.dynamite_cronetdynamite@242632114@24.26.32 (230800-0):9)
                                                                                                    	at adtm.c(:com.google.android.gms@242632114@24.26.32 (230800-650348549):50) 
                                                                                                    	at adtm.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):76) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at adys.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):8) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    Caused by: m.mc: Exception in CronetUrlRequest: net::ERR_TIMED_OUT, ErrorCode=4, InternalErrorCode=-7, Retryable=true
                                                                                                    	at org.chromium.net.impl.CronetUrlRequest.onError(:com.google.android.gms.dynamite_cronetdynamite@242632114@24.26.32 (230800-0):4)
2025-05-24 18:09:04.351  1539-4757  PhenotypeFlagCommitter  com.google.android.gms.persistent    E  Retrieving snapshot for com.google.android.gms.phenotype failed (Ask Gemini)
                                                                                                    java.util.concurrent.TimeoutException: Timed out waiting for Task
                                                                                                    	at brml.n(:com.google.android.gms@242632114@24.26.32 (230800-650348549):53)
                                                                                                    	at bmyl.i(:com.google.android.gms@242632114@24.26.32 (230800-650348549):13)
                                                                                                    	at bmyl.j(:com.google.android.gms@242632114@24.26.32 (230800-650348549):42)
                                                                                                    	at bmyl.h(:com.google.android.gms@242632114@24.26.32 (230800-650348549):4)
                                                                                                    	at bnan.c(:com.google.android.gms@242632114@24.26.32 (230800-650348549):23)
                                                                                                    	at com.google.android.gms.phenotype.sync.HeterodyneSyncTaskChimeraService.d(:com.google.android.gms@242632114@24.26.32 (230800-650348549):1208)
                                                                                                    	at com.google.android.gms.phenotype.sync.HeterodyneSyncTaskChimeraService.a(:com.google.android.gms@242632114@24.26.32 (230800-650348549):126)
                                                                                                    	at avkn.call(:com.google.android.gms@242632114@24.26.32 (230800-650348549):32)
                                                                                                    	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
                                                                                                    	at adtm.c(:com.google.android.gms@242632114@24.26.32 (230800-650348549):50)
                                                                                                    	at adtm.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):76)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at adys.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):8)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 18:09:04.353  1539-2146  NetworkScheduler.ATC    com.google.android.gms.persistent    E  Trying to release unacquired lock: com.google.android.gms/.phenotype.service.sync.PhenotypeConfigurator [CONTEXT service_id=218 ]
2025-05-24 18:09:04.386  1539-2146  NetworkScheduler.ATC    com.google.android.gms.persistent    E  Trying to release unacquired lock: com.google.android.gms/.audit.upload.AuditGcmTaskService [CONTEXT service_id=218 ]
2025-05-24 18:09:04.388  1539-2146  NetworkScheduler.ATC    com.google.android.gms.persistent    E  Trying to release unacquired lock: com.google.android.gms/.phenotype.service.sync.PhenotypeConfigurator [CONTEXT service_id=218 ]
2025-05-24 18:09:04.389  1539-2146  NetworkScheduler.ATC    com.google.android.gms.persistent    E  Trying to release unacquired lock: com.google.android.gms/.audit.upload.AuditGcmTaskService [CONTEXT service_id=218 ]
2025-05-24 18:09:04.471  1890-4957  HeterodyneSyncTaskChime com.google.android.gms.persistent    E  Sync task failure [CONTEXT service_id=51 ] (Ask Gemini)
                                                                                                    bmzq: 29504: Network error
                                                                                                    	at bnen.M(:com.google.android.gms@242632114@24.26.32 (230800-650348549):191)
                                                                                                    	at bnen.q(:com.google.android.gms@242632114@24.26.32 (230800-650348549):113)
                                                                                                    	at bnen.B(:com.google.android.gms@242632114@24.26.32 (230800-650348549):10)
                                                                                                    	at bnen.p(:com.google.android.gms@242632114@24.26.32 (230800-650348549):11)
                                                                                                    	at com.google.android.gms.phenotype.sync.HeterodyneSyncTaskChimeraService.d(:com.google.android.gms@242632114@24.26.32 (230800-650348549):270)
                                                                                                    	at com.google.android.gms.phenotype.sync.HeterodyneSyncTaskChimeraService.a(:com.google.android.gms@242632114@24.26.32 (230800-650348549):126)
                                                                                                    	at avkn.call(:com.google.android.gms@242632114@24.26.32 (230800-650348549):32)
                                                                                                    	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
                                                                                                    	at adtm.c(:com.google.android.gms@242632114@24.26.32 (230800-650348549):50)
                                                                                                    	at adtm.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):76)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at adys.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):8)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    Caused by: java.io.InterruptedIOException: request interrupted
                                                                                                    	at bnet.a(:com.google.android.gms@242632114@24.26.32 (230800-650348549):479)
                                                                                                    	at bnen.M(:com.google.android.gms@242632114@24.26.32 (230800-650348549):17)
                                                                                                    	at bnen.q(:com.google.android.gms@242632114@24.26.32 (230800-650348549):113) 
                                                                                                    	at bnen.B(:com.google.android.gms@242632114@24.26.32 (230800-650348549):10) 
                                                                                                    	at bnen.p(:com.google.android.gms@242632114@24.26.32 (230800-650348549):11) 
                                                                                                    	at com.google.android.gms.phenotype.sync.HeterodyneSyncTaskChimeraService.d(:com.google.android.gms@242632114@24.26.32 (230800-650348549):270) 
                                                                                                    	at com.google.android.gms.phenotype.sync.HeterodyneSyncTaskChimeraService.a(:com.google.android.gms@242632114@24.26.32 (230800-650348549):126) 
                                                                                                    	at avkn.call(:com.google.android.gms@242632114@24.26.32 (230800-650348549):32) 
                                                                                                    	at java.util.concurrent.FutureTask.run(FutureTask.java:264) 
                                                                                                    	at adtm.c(:com.google.android.gms@242632114@24.26.32 (230800-650348549):50) 
                                                                                                    	at adtm.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):76) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at adys.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):8) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
2025-05-24 18:09:06.443  1539-1933  bnbi                    com.google.android.gms.persistent    E  Phenotype API error. Event: # ddtm@167c2a6d, EventCode: REGISTER_SYNC [CONTEXT service_id=51 ] (Ask Gemini)
                                                                                                    bmzq: 29504: Network error
                                                                                                    	at bnen.M(:com.google.android.gms@242632114@24.26.32 (230800-650348549):191)
                                                                                                    	at bnen.q(:com.google.android.gms@242632114@24.26.32 (230800-650348549):113)
                                                                                                    	at bnen.B(:com.google.android.gms@242632114@24.26.32 (230800-650348549):10)
                                                                                                    	at bnen.r(:com.google.android.gms@242632114@24.26.32 (230800-650348549):21)
                                                                                                    	at bncv.i(:com.google.android.gms@242632114@24.26.32 (230800-650348549):187)
                                                                                                    	at bnbi.h(:com.google.android.gms@242632114@24.26.32 (230800-650348549):18)
                                                                                                    	at bnbi.f(:com.google.android.gms@242632114@24.26.32 (230800-650348549):11)
                                                                                                    	at auvg.eV(:com.google.android.gms@242632114@24.26.32 (230800-650348549):1)
                                                                                                    	at auvl.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):132)
                                                                                                    	at cjtd.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):21)
                                                                                                    	at adtm.c(:com.google.android.gms@242632114@24.26.32 (230800-650348549):50)
                                                                                                    	at adtm.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):97)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at adys.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):8)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    Caused by: java.io.IOException: request causes GmsNetworkException without UrlResponseInfo at execution
                                                                                                    	at bnet.a(:com.google.android.gms@242632114@24.26.32 (230800-650348549):429)
                                                                                                    	at bnen.M(:com.google.android.gms@242632114@24.26.32 (230800-650348549):17)
                                                                                                    	at bnen.q(:com.google.android.gms@242632114@24.26.32 (230800-650348549):113) 
                                                                                                    	at bnen.B(:com.google.android.gms@242632114@24.26.32 (230800-650348549):10) 
                                                                                                    	at bnen.r(:com.google.android.gms@242632114@24.26.32 (230800-650348549):21) 
                                                                                                    	at bncv.i(:com.google.android.gms@242632114@24.26.32 (230800-650348549):187) 
                                                                                                    	at bnbi.h(:com.google.android.gms@242632114@24.26.32 (230800-650348549):18) 
                                                                                                    	at bnbi.f(:com.google.android.gms@242632114@24.26.32 (230800-650348549):11) 
                                                                                                    	at auvg.eV(:com.google.android.gms@242632114@24.26.32 (230800-650348549):1) 
                                                                                                    	at auvl.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):132) 
                                                                                                    	at cjtd.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):21) 
                                                                                                    	at adtm.c(:com.google.android.gms@242632114@24.26.32 (230800-650348549):50) 
                                                                                                    	at adtm.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):97) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at adys.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):8) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    Caused by: avee: Failed to process request
                                                                                                    	at avhq.b(:com.google.android.gms@242632114@24.26.32 (230800-650348549):19)
                                                                                                    	at avie.onFailed(:com.google.android.gms@242632114@24.26.32 (230800-650348549):15)
                                                                                                    	at m.ms.onFailed(:com.google.android.gms.dynamite_cronetdynamite@242632114@24.26.32 (230800-0):3)
                                                                                                    	at m.lt.run(:com.google.android.gms.dynamite_cronetdynamite@242632114@24.26.32 (230800-0):9)
                                                                                                    	at adtm.c(:com.google.android.gms@242632114@24.26.32 (230800-650348549):50)
                                                                                                    	at adtm.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):76)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at adys.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):8) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    Caused by: m.mf: Exception in CronetUrlRequest: net::ERR_QUIC_PROTOCOL_ERROR, ErrorCode=10, InternalErrorCode=-356, Retryable=false, QuicDetailedErrorCode=25
                                                                                                    	at org.chromium.net.impl.CronetUrlRequest.onError(:com.google.android.gms.dynamite_cronetdynamite@242632114@24.26.32 (230800-0):6)
2025-05-24 18:09:06.475  1890-4957  PhenotypeFlagCommitter  com.google.android.gms.persistent    E  Retrieving snapshot for com.google.android.gms.phenotype failed (Ask Gemini)
                                                                                                    java.util.concurrent.TimeoutException: Timed out waiting for Task
                                                                                                    	at brml.n(:com.google.android.gms@242632114@24.26.32 (230800-650348549):53)
                                                                                                    	at bmyl.i(:com.google.android.gms@242632114@24.26.32 (230800-650348549):13)
                                                                                                    	at bmyl.j(:com.google.android.gms@242632114@24.26.32 (230800-650348549):42)
                                                                                                    	at bmyl.h(:com.google.android.gms@242632114@24.26.32 (230800-650348549):4)
                                                                                                    	at bnan.c(:com.google.android.gms@242632114@24.26.32 (230800-650348549):23)
                                                                                                    	at com.google.android.gms.phenotype.sync.HeterodyneSyncTaskChimeraService.d(:com.google.android.gms@242632114@24.26.32 (230800-650348549):1208)
                                                                                                    	at com.google.android.gms.phenotype.sync.HeterodyneSyncTaskChimeraService.a(:com.google.android.gms@242632114@24.26.32 (230800-650348549):126)
                                                                                                    	at avkn.call(:com.google.android.gms@242632114@24.26.32 (230800-650348549):32)
                                                                                                    	at java.util.concurrent.FutureTask.run(FutureTask.java:264)
                                                                                                    	at adtm.c(:com.google.android.gms@242632114@24.26.32 (230800-650348549):50)
                                                                                                    	at adtm.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):76)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at adys.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):8)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 18:09:06.591  1890-4548  bnbi                    com.google.android.gms.persistent    E  Phenotype API error. Event: # ddtm@1bd0a745, EventCode: REGISTER_SYNC [CONTEXT service_id=51 ] (Ask Gemini)
                                                                                                    bmzq: 29504: Network error
                                                                                                    	at bnen.M(:com.google.android.gms@242632114@24.26.32 (230800-650348549):191)
                                                                                                    	at bnen.q(:com.google.android.gms@242632114@24.26.32 (230800-650348549):113)
                                                                                                    	at bnen.B(:com.google.android.gms@242632114@24.26.32 (230800-650348549):10)
                                                                                                    	at bnen.r(:com.google.android.gms@242632114@24.26.32 (230800-650348549):21)
                                                                                                    	at bncv.i(:com.google.android.gms@242632114@24.26.32 (230800-650348549):187)
                                                                                                    	at bnbi.h(:com.google.android.gms@242632114@24.26.32 (230800-650348549):18)
                                                                                                    	at bnbi.f(:com.google.android.gms@242632114@24.26.32 (230800-650348549):11)
                                                                                                    	at auvg.eV(:com.google.android.gms@242632114@24.26.32 (230800-650348549):1)
                                                                                                    	at auvl.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):132)
                                                                                                    	at cjtd.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):21)
                                                                                                    	at adtm.c(:com.google.android.gms@242632114@24.26.32 (230800-650348549):50)
                                                                                                    	at adtm.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):97)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at adys.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):8)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    Caused by: java.io.IOException: request causes GmsNetworkException without UrlResponseInfo at execution
                                                                                                    	at bnet.a(:com.google.android.gms@242632114@24.26.32 (230800-650348549):429)
                                                                                                    	at bnen.M(:com.google.android.gms@242632114@24.26.32 (230800-650348549):17)
                                                                                                    	at bnen.q(:com.google.android.gms@242632114@24.26.32 (230800-650348549):113) 
                                                                                                    	at bnen.B(:com.google.android.gms@242632114@24.26.32 (230800-650348549):10) 
                                                                                                    	at bnen.r(:com.google.android.gms@242632114@24.26.32 (230800-650348549):21) 
                                                                                                    	at bncv.i(:com.google.android.gms@242632114@24.26.32 (230800-650348549):187) 
                                                                                                    	at bnbi.h(:com.google.android.gms@242632114@24.26.32 (230800-650348549):18) 
                                                                                                    	at bnbi.f(:com.google.android.gms@242632114@24.26.32 (230800-650348549):11) 
                                                                                                    	at auvg.eV(:com.google.android.gms@242632114@24.26.32 (230800-650348549):1) 
                                                                                                    	at auvl.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):132) 
                                                                                                    	at cjtd.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):21) 
                                                                                                    	at adtm.c(:com.google.android.gms@242632114@24.26.32 (230800-650348549):50) 
                                                                                                    	at adtm.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):97) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at adys.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):8) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    Caused by: avee: Failed to process request
                                                                                                    	at avhq.b(:com.google.android.gms@242632114@24.26.32 (230800-650348549):19)
                                                                                                    	at avie.onFailed(:com.google.android.gms@242632114@24.26.32 (230800-650348549):15)
                                                                                                    	at m.ms.onFailed(:com.google.android.gms.dynamite_cronetdynamite@242632114@24.26.32 (230800-0):3)
                                                                                                    	at m.lt.run(:com.google.android.gms.dynamite_cronetdynamite@242632114@24.26.32 (230800-0):9)
                                                                                                    	at adtm.c(:com.google.android.gms@242632114@24.26.32 (230800-650348549):50)
                                                                                                    	at adtm.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):76)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at adys.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):8) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    Caused by: m.mc: Exception in CronetUrlRequest: net::ERR_TIMED_OUT, ErrorCode=4, InternalErrorCode=-7, Retryable=true
                                                                                                    	at org.chromium.net.impl.CronetUrlRequest.onError(:com.google.android.gms.dynamite_cronetdynamite@242632114@24.26.32 (230800-0):4)
2025-05-24 18:09:10.591  1890-2350  bnbi                    com.google.android.gms.persistent    E  Phenotype API error. Event: # ddtm@1936e3f2, EventCode: REGISTER_SYNC [CONTEXT service_id=51 ] (Ask Gemini)
                                                                                                    bmzq: 29504: Network error
                                                                                                    	at bnen.M(:com.google.android.gms@242632114@24.26.32 (230800-650348549):191)
                                                                                                    	at bnen.q(:com.google.android.gms@242632114@24.26.32 (230800-650348549):113)
                                                                                                    	at bnen.B(:com.google.android.gms@242632114@24.26.32 (230800-650348549):10)
                                                                                                    	at bnen.r(:com.google.android.gms@242632114@24.26.32 (230800-650348549):21)
                                                                                                    	at bncv.i(:com.google.android.gms@242632114@24.26.32 (230800-650348549):187)
                                                                                                    	at bnbi.h(:com.google.android.gms@242632114@24.26.32 (230800-650348549):18)
                                                                                                    	at bnbi.f(:com.google.android.gms@242632114@24.26.32 (230800-650348549):11)
                                                                                                    	at auvg.eV(:com.google.android.gms@242632114@24.26.32 (230800-650348549):1)
                                                                                                    	at auvl.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):132)
                                                                                                    	at cjtd.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):21)
                                                                                                    	at adtm.c(:com.google.android.gms@242632114@24.26.32 (230800-650348549):50)
                                                                                                    	at adtm.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):97)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at adys.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):8)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    Caused by: java.io.IOException: request causes GmsNetworkException without UrlResponseInfo at execution
                                                                                                    	at bnet.a(:com.google.android.gms@242632114@24.26.32 (230800-650348549):429)
                                                                                                    	at bnen.M(:com.google.android.gms@242632114@24.26.32 (230800-650348549):17)
                                                                                                    	at bnen.q(:com.google.android.gms@242632114@24.26.32 (230800-650348549):113) 
                                                                                                    	at bnen.B(:com.google.android.gms@242632114@24.26.32 (230800-650348549):10) 
                                                                                                    	at bnen.r(:com.google.android.gms@242632114@24.26.32 (230800-650348549):21) 
                                                                                                    	at bncv.i(:com.google.android.gms@242632114@24.26.32 (230800-650348549):187) 
                                                                                                    	at bnbi.h(:com.google.android.gms@242632114@24.26.32 (230800-650348549):18) 
                                                                                                    	at bnbi.f(:com.google.android.gms@242632114@24.26.32 (230800-650348549):11) 
                                                                                                    	at auvg.eV(:com.google.android.gms@242632114@24.26.32 (230800-650348549):1) 
                                                                                                    	at auvl.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):132) 
                                                                                                    	at cjtd.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):21) 
                                                                                                    	at adtm.c(:com.google.android.gms@242632114@24.26.32 (230800-650348549):50) 
                                                                                                    	at adtm.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):97) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at adys.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):8) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    Caused by: avee: Failed to process request
                                                                                                    	at avhq.b(:com.google.android.gms@242632114@24.26.32 (230800-650348549):19)
                                                                                                    	at avie.onFailed(:com.google.android.gms@242632114@24.26.32 (230800-650348549):15)
                                                                                                    	at m.ms.onFailed(:com.google.android.gms.dynamite_cronetdynamite@242632114@24.26.32 (230800-0):3)
                                                                                                    	at m.lt.run(:com.google.android.gms.dynamite_cronetdynamite@242632114@24.26.32 (230800-0):9)
                                                                                                    	at adtm.c(:com.google.android.gms@242632114@24.26.32 (230800-650348549):50)
                                                                                                    	at adtm.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):76)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137) 
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637) 
                                                                                                    	at adys.run(:com.google.android.gms@242632114@24.26.32 (230800-650348549):8) 
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
                                                                                                    Caused by: m.mc: Exception in CronetUrlRequest: net::ERR_TIMED_OUT, ErrorCode=4, InternalErrorCode=-7, Retryable=true
                                                                                                    	at org.chromium.net.impl.CronetUrlRequest.onError(:com.google.android.gms.dynamite_cronetdynamite@242632114@24.26.32 (230800-0):4)
2025-05-24 18:09:45.569  2285-2592  RadioStationSyncImpl    com.google.android.carassistant      E  Error retrieving the OEM radio App's browse tree (Ask Gemini)
                                                                                                    java.lang.IllegalStateException: resolveInfo is null
                                                                                                    	at izy.c(PG:28)
                                                                                                    	at jdj.a(PG:35)
                                                                                                    	at jcz.b(PG:42)
                                                                                                    	at snx.a(PG:145)
                                                                                                    	at uzf.a(PG:105)
                                                                                                    	at uzf.a(PG:105)
                                                                                                    	at yap.a(PG:3)
                                                                                                    	at xzv.run(PG:19)
                                                                                                    	at yar.run(PG:5)
                                                                                                    	at xyq.execute(PG:1)
                                                                                                    	at xos.x(PG:6)
                                                                                                    	at wab.bi(PG:5)
                                                                                                    	at vbt.b(PG:70)
                                                                                                    	at adkk.k(PG:51)
                                                                                                    	at ugo.b(PG:21)
                                                                                                    	at com.google.apps.tiktok.contrib.work.TikTokListenableWorker.b(PG:85)
                                                                                                    	at crj.eF(PG:115)
                                                                                                    	at acvf.fW(PG:12)
                                                                                                    	at aday.run(PG:109)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:942)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:7924)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
2025-05-24 18:09:54.521  6298-6320  OpenGLRenderer          com.example.aimusicplayer            E  Unable to match the desired swap behavior.
2025-05-24 18:10:01.409  6298-6298  QrCodeProcessor         com.example.aimusicplayer            E  获取二维码Key异常 (Ask Gemini)
                                                                                                    retrofit2.HttpException: HTTP 400 Bad Request
                                                                                                    	at retrofit2.KotlinExtensions$await$2$2.onResponse(KotlinExtensions.kt:53)
                                                                                                    	at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
                                                                                                    	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:519)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 18:10:02.984  6298-6320  OpenGLRenderer          com.example.aimusicplayer            E  Unable to match the desired swap behavior.
2025-05-24 18:10:03.221  6298-6298  QrCodeProcessor         com.example.aimusicplayer            E  获取二维码Key异常 (Ask Gemini)
                                                                                                    retrofit2.HttpException: HTTP 400 Bad Request
                                                                                                    	at retrofit2.KotlinExtensions$await$2$2.onResponse(KotlinExtensions.kt:53)
                                                                                                    	at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
                                                                                                    	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:519)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 18:10:04.264  6298-6298  QrCodeProcessor         com.example.aimusicplayer            E  获取二维码Key异常 (Ask Gemini)
                                                                                                    retrofit2.HttpException: HTTP 400 Bad Request
                                                                                                    	at retrofit2.KotlinExtensions$await$2$2.onResponse(KotlinExtensions.kt:53)
                                                                                                    	at retrofit2.OkHttpCall$1.onResponse(OkHttpCall.java:161)
                                                                                                    	at okhttp3.internal.connection.RealCall$AsyncCall.run(RealCall.kt:519)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
2025-05-24 18:10:15.718  6298-6298  FlowViewModel           com.example.aimusicplayer            E  Error in ViewModel (Ask Gemini)
                                                                                                    java.lang.Exception: 未能获取账号信息
                                                                                                    	at com.example.aimusicplayer.viewmodel.LoginViewModel.getUserInfo(LoginViewModel.kt:319)
                                                                                                    	at com.example.aimusicplayer.viewmodel.LoginViewModel.access$getUserInfo(LoginViewModel.kt:30)
                                                                                                    	at com.example.aimusicplayer.viewmodel.LoginViewModel$getUserInfo$1.invokeSuspend(Unknown Source:14)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:942)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:7924)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
2025-05-24 18:10:15.720  6298-6298  GlobalErrorHandler      com.example.aimusicplayer            E  处理错误 (Ask Gemini)
                                                                                                    java.lang.Exception: 未能获取账号信息
                                                                                                    	at com.example.aimusicplayer.viewmodel.LoginViewModel.getUserInfo(LoginViewModel.kt:319)
                                                                                                    	at com.example.aimusicplayer.viewmodel.LoginViewModel.access$getUserInfo(LoginViewModel.kt:30)
                                                                                                    	at com.example.aimusicplayer.viewmodel.LoginViewModel$getUserInfo$1.invokeSuspend(Unknown Source:14)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:942)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:7924)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
2025-05-24 18:10:15.725  6298-6298  LoginActivity           com.example.aimusicplayer            E  错误信息: 未能获取账号信息
2025-05-24 18:10:15.800  6298-6320  OpenGLRenderer          com.example.aimusicplayer            E  Unable to match the desired swap behavior.
2025-05-24 18:10:19.394   344-344   android.ha...rvice-mock <EMAIL>  E  Failed to getEnergyData
2025-05-24 18:10:31.705  6298-6298  FlowViewModel           com.example.aimusicplayer            E  Error in ViewModel (Ask Gemini)
                                                                                                    java.lang.Exception: 未能获取账号信息
                                                                                                    	at com.example.aimusicplayer.viewmodel.LoginViewModel.getUserInfo(LoginViewModel.kt:319)
                                                                                                    	at com.example.aimusicplayer.viewmodel.LoginViewModel.access$getUserInfo(LoginViewModel.kt:30)
                                                                                                    	at com.example.aimusicplayer.viewmodel.LoginViewModel$getUserInfo$1.invokeSuspend(Unknown Source:14)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:942)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:7924)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
2025-05-24 18:10:31.705  6298-6298  GlobalErrorHandler      com.example.aimusicplayer            E  处理错误 (Ask Gemini)
                                                                                                    java.lang.Exception: 未能获取账号信息
                                                                                                    	at com.example.aimusicplayer.viewmodel.LoginViewModel.getUserInfo(LoginViewModel.kt:319)
                                                                                                    	at com.example.aimusicplayer.viewmodel.LoginViewModel.access$getUserInfo(LoginViewModel.kt:30)
                                                                                                    	at com.example.aimusicplayer.viewmodel.LoginViewModel$getUserInfo$1.invokeSuspend(Unknown Source:14)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:942)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:7924)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
2025-05-24 18:10:31.709  6298-6298  LoginActivity           com.example.aimusicplayer            E  错误信息: 未能获取账号信息
2025-05-24 18:10:31.783  6298-6320  OpenGLRenderer          com.example.aimusicplayer            E  Unable to match the desired swap behavior.
2025-05-24 18:10:39.684  6298-6298  FlowViewModel           com.example.aimusicplayer            E  Error in ViewModel (Ask Gemini)
                                                                                                    java.lang.Exception: 未能获取账号信息
                                                                                                    	at com.example.aimusicplayer.viewmodel.LoginViewModel.getUserInfo(LoginViewModel.kt:319)
                                                                                                    	at com.example.aimusicplayer.viewmodel.LoginViewModel.access$getUserInfo(LoginViewModel.kt:30)
                                                                                                    	at com.example.aimusicplayer.viewmodel.LoginViewModel$getUserInfo$1.invokeSuspend(Unknown Source:14)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:942)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:7924)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
2025-05-24 18:10:39.684  6298-6298  GlobalErrorHandler      com.example.aimusicplayer            E  处理错误 (Ask Gemini)
                                                                                                    java.lang.Exception: 未能获取账号信息
                                                                                                    	at com.example.aimusicplayer.viewmodel.LoginViewModel.getUserInfo(LoginViewModel.kt:319)
                                                                                                    	at com.example.aimusicplayer.viewmodel.LoginViewModel.access$getUserInfo(LoginViewModel.kt:30)
                                                                                                    	at com.example.aimusicplayer.viewmodel.LoginViewModel$getUserInfo$1.invokeSuspend(Unknown Source:14)
                                                                                                    	at kotlin.coroutines.jvm.internal.BaseContinuationImpl.resumeWith(ContinuationImpl.kt:33)
                                                                                                    	at kotlinx.coroutines.DispatchedTask.run(DispatchedTask.kt:108)
                                                                                                    	at android.os.Handler.handleCallback(Handler.java:942)
                                                                                                    	at android.os.Handler.dispatchMessage(Handler.java:99)
                                                                                                    	at android.os.Looper.loopOnce(Looper.java:201)
                                                                                                    	at android.os.Looper.loop(Looper.java:288)
                                                                                                    	at android.app.ActivityThread.main(ActivityThread.java:7924)
                                                                                                    	at java.lang.reflect.Method.invoke(Native Method)
                                                                                                    	at com.android.internal.os.RuntimeInit$MethodAndArgsCaller.run(RuntimeInit.java:548)
                                                                                                    	at com.android.internal.os.ZygoteInit.main(ZygoteInit.java:936)
2025-05-24 18:10:39.692  6298-6298  LoginActivity           com.example.aimusicplayer            E  错误信息: 未能获取账号信息
2025-05-24 18:10:39.764  6298-6320  OpenGLRenderer          com.example.aimusicplayer            E  Unable to match the desired swap behavior.
2025-05-24 18:10:39.935   616-795   WifiScoringParams       system_server                        E  Invalid frequency(-1), using 5G as default rssi array
2025-05-24 18:10:55.200  2980-6746  FirebaseInstanceId      com.android.vending                  E  Failed to get FIS auth token (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.google.firebase.installations.FirebaseInstallationsException: Firebase Installations Service is unavailable. Please try again later.
                                                                                                    	at vtw.iJ(PG:32)
                                                                                                    	at vtw.G(PG:31)
                                                                                                    	at afgv.i(PG:183)
                                                                                                    	at acdh.a(PG:191)
                                                                                                    	at ujn.run(PG:251)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at vbl.run(PG:7)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    Caused by: com.google.firebase.installations.FirebaseInstallationsException: Firebase Installations Service is unavailable. Please try again later.
                                                                                                    	at acei.b(PG:356)
                                                                                                    	at acdu.run(PG:233)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
2025-05-24 18:10:59.735  2980-3224  PlayCommon              com.android.vending                  E  [218] ypn.j(1620): Failed to connect to server for server timestamp: java.net.SocketTimeoutException: failed to connect to play.googleapis.com/2001:4860:4802:38::223 (port 443) from /fec0::5054:ff:fe12:3456 (port 45914) after 40000ms
2025-05-24 18:11:00.096  3304-6759  FirebaseInstanceId      com.android.vending                  E  Failed to get FIS auth token (Ask Gemini)
                                                                                                    java.util.concurrent.ExecutionException: com.google.firebase.installations.FirebaseInstallationsException: Firebase Installations Service is unavailable. Please try again later.
                                                                                                    	at vtw.iJ(PG:32)
                                                                                                    	at vtw.G(PG:31)
                                                                                                    	at afgv.i(PG:183)
                                                                                                    	at acdh.a(PG:191)
                                                                                                    	at ujn.run(PG:251)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at vbl.run(PG:7)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012)
                                                                                                    Caused by: com.google.firebase.installations.FirebaseInstallationsException: Firebase Installations Service is unavailable. Please try again later.
                                                                                                    	at acei.b(PG:356)
                                                                                                    	at acdu.run(PG:233)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1137)
                                                                                                    	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:637)
                                                                                                    	at java.lang.Thread.run(Thread.java:1012) 
2025-05-24 18:11:05.131  3304-3540  PlayCommon              com.android.vending                  E  [239] ypn.j(1620): Failed to connect to server for server timestamp: java.net.SocketTimeoutException: failed to connect to play.googleapis.com/2001:4860:4802:38::223 (port 443) from /fec0::5054:ff:fe12:3456 (port 52302) after 40000ms
2025-05-24 18:11:17.977  1539-6166  WakeLock                com.google.android.gms.persistent    E  GCM_HB_ALARM release without a matched acquire