# PowerShell 快速编译脚本
# 解决 gradlew.bat 无法识别的问题

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "AI音乐播放器 - PowerShell快速编译脚本" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# 检查是否在正确的目录
if (-not (Test-Path ".\gradlew.bat")) {
    Write-Host "❌ 错误: gradlew.bat 文件不存在" -ForegroundColor Red
    Write-Host "请确保在项目根目录运行此脚本" -ForegroundColor Yellow
    Read-Host "按任意键退出"
    exit 1
}

Write-Host "✅ 找到 gradlew.bat 文件" -ForegroundColor Green
Write-Host "当前目录: $(Get-Location)" -ForegroundColor Gray
Write-Host ""

# 显示菜单
Write-Host "请选择编译选项:" -ForegroundColor Yellow
Write-Host "1. 快速验证编译 (推荐)" -ForegroundColor White
Write-Host "2. 完整编译" -ForegroundColor White
Write-Host "3. 清理项目" -ForegroundColor White
Write-Host "4. 检查项目状态" -ForegroundColor White
Write-Host "5. 退出" -ForegroundColor White
Write-Host ""

$choice = Read-Host "请输入选项 (1-5)"

switch ($choice) {
    "1" {
        Write-Host ""
        Write-Host "🚀 开始快速验证编译..." -ForegroundColor Cyan
        Write-Host "========================================" -ForegroundColor Cyan
        
        # 使用 cmd 调用 gradlew.bat，避免PowerShell识别问题
        Write-Host "[1/2] 检查Kotlin编译..." -ForegroundColor Yellow
        $result1 = cmd /c "gradlew.bat compileDebugKotlin --console=plain --no-daemon --quiet"
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ Kotlin编译成功" -ForegroundColor Green
        } else {
            Write-Host "❌ Kotlin编译失败" -ForegroundColor Red
            Write-Host $result1
            Read-Host "按任意键退出"
            exit 1
        }
        
        Write-Host "[2/2] 检查资源处理..." -ForegroundColor Yellow
        $result2 = cmd /c "gradlew.bat processDebugResources --console=plain --no-daemon --quiet"
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ 资源处理成功" -ForegroundColor Green
        } else {
            Write-Host "❌ 资源处理失败" -ForegroundColor Red
            Write-Host $result2
            Read-Host "按任意键退出"
            exit 1
        }
        
        Write-Host ""
        Write-Host "🎉 快速验证完成！项目可以正常编译" -ForegroundColor Green
    }
    
    "2" {
        Write-Host ""
        Write-Host "🔨 开始完整编译..." -ForegroundColor Cyan
        cmd /c "gradlew.bat assembleDebug --console=plain --no-daemon"
    }
    
    "3" {
        Write-Host ""
        Write-Host "🧹 清理项目..." -ForegroundColor Cyan
        cmd /c "gradlew.bat clean --console=plain --no-daemon"
    }
    
    "4" {
        Write-Host ""
        Write-Host "📊 检查项目状态..." -ForegroundColor Cyan
        cmd /c "gradlew.bat tasks --console=plain --no-daemon"
    }
    
    "5" {
        Write-Host "退出脚本" -ForegroundColor Gray
        exit 0
    }
    
    default {
        Write-Host "❌ 无效选项" -ForegroundColor Red
        Read-Host "按任意键退出"
        exit 1
    }
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "编译完成！" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Read-Host "按任意键退出"
